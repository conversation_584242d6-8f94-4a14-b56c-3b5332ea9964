package com.thyview.services

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.thyview.models.reviews.Comment
import com.thyview.models.reviews.Post
import timber.log.Timber
import javax.inject.Inject

/**
 * Production implementation of IPostService using REST API.
 * 
 * Note: Class name is kept as FirebasePostService for backward compatibility,
 * but implementation has been changed to use REST API.
 */
class AWSPostService @Inject constructor(
    private val postApiService: PostApiService,
    private val gson: Gson
) : IPostService {

    val useMockData = true

    private val mockComments = mapOf(
        "mock1" to listOf(
            Comment(
                id = "comment1",
                externalAuthorId = "user2",
                text = "<PERSON><PERSON><PERSON>'s performance in 'Athadu' is unparalleled!",
                parentCommentId = null,
                imageUrl = "https://upload.wikimedia.org/wikipedia/en/0/01/Athadu_poster.jpg",
                createdAt = java.time.Instant.now().minusSeconds(3600).toString(),
                replyCount = 2,
                username = "<PERSON>gh<PERSON>"
            ),
            Comment(
                id = "comment2",
                externalAuthorId = "user3",
                text = "Did anyone notice the cinematography in 'Baahubali'? Simply mind-blowing!",
                parentCommentId = null,
                imageUrl = "https://upload.wikimedia.org/wikipedia/en/5/5f/Baahubali_The_Beginning_poster.jpg",
                createdAt = java.time.Instant.now().minusSeconds(2400).toString(),
                replyCount = 1,
                username = "Sravani"
            ),
            Comment(
                id = "comment3",
                externalAuthorId = "user5",
                text = "Can't get over the BGM in 'Arjun Reddy'.",
                parentCommentId = null,
                imageUrl = "https://upload.wikimedia.org/wikipedia/en/6/63/Arjun_Reddy.jpg",
                createdAt = java.time.Instant.now().minusSeconds(1200).toString(),
                replyCount = 0,
                username = "Vikram"
            )
        ),
        "mock2" to listOf(
            Comment(
                id = "comment4",
                externalAuthorId = "user1",
                text = "How long did it take to notice the storyline changes in 'Magadheera'?",
                parentCommentId = null,
                imageUrl = "https://upload.wikimedia.org/wikipedia/en/1/1e/Magadheera_Poster.jpg",
                createdAt = java.time.Instant.now().minusSeconds(3000).toString(),
                replyCount = 1,
                username = "Arjun"
            ),
            Comment(
                id = "comment5",
                externalAuthorId = "user4",
                text = "'Eega' was a game changer for me too, loved the creativity.",
                parentCommentId = null,
                imageUrl = "https://upload.wikimedia.org/wikipedia/en/2/2e/Eega_poster.jpg",
                createdAt = java.time.Instant.now().minusSeconds(2000).toString(),
                replyCount = 0,
                username = "Priya"
            )
        ),
        "mock3" to listOf(
            Comment(
                id = "comment6",
                externalAuthorId = "user5",
                text = "I've been recommending 'Jersey' for its inspirational storyline!",
                parentCommentId = null,
                imageUrl = "https://upload.wikimedia.org/wikipedia/en/6/6d/Jersey_film_poster.jpg",
                createdAt = java.time.Instant.now().minusSeconds(1500).toString(),
                replyCount = 0,
                username = "Vikram"
            )
        )
    )


    // Mock replies to comments
    private val mockReplies = mapOf(
        "comment1" to listOf(
            Comment(
                id = "reply1",
                externalAuthorId = "user1",
                text = "Absolutely, Trivikram did a fantastic job!",
                parentCommentId = "comment1",
                imageUrl = null,
                createdAt = java.time.Instant.now().minusSeconds(3000).toString(),
                replyCount = 0,
                username = "Arjun"
            ),
            Comment(
                id = "reply2",
                externalAuthorId = "user2",
                text = "Yes, the dialogues were very impactful too.",
                parentCommentId = "comment1",
                imageUrl = null,
                createdAt = java.time.Instant.now().minusSeconds(2800).toString(),
                replyCount = 0,
                username = "Raghu"
            )
        ),
        "comment2" to listOf(
            Comment(
                id = "reply3",
                externalAuthorId = "user1",
                text = "Keeravani garu's music indeed!",
                parentCommentId = "comment2",
                imageUrl = null,
                createdAt = java.time.Instant.now().minusSeconds(2000).toString(),
                replyCount = 0,
                username = "Arjun"
            )
        ),
        "comment4" to listOf(
            Comment(
                id = "reply4",
                externalAuthorId = "user2",
                text = "About 2 weeks, but worth every second!",
                parentCommentId = "comment4",
                imageUrl = null,
                createdAt = java.time.Instant.now().minusSeconds(2500).toString(),
                replyCount = 0,
                username = "Raghu"
            )
        )
    )

    private val mockPosts = listOf(
        Post(
            id = "mock1",
            externalAuthorId = "user1",
            username = "Arjun",
            profileImageUrl = "https://example.com/profile1.jpg",
            content = "RRR is just phenomenal! What are your thoughts?",
            imageUrl = "https://upload.wikimedia.org/wikipedia/en/d/d7/RRR_Poster.jpg",
            likeCount = 120,
            commentCount = 5,
            createdAt = java.time.Instant.now().minusSeconds(7200).toString(),
            liked = true
        ),
        Post(
            id = "mock2",
            externalAuthorId = "user2",
            username = "Raghu",
            profileImageUrl = "https://example.com/profile2.jpg",
            content = "The dialogues in 'Pushpa' stay with you long after the movie ends.",
            imageUrl = "https://upload.wikimedia.org/wikipedia/en/5/5b/Pushpa_-_The_Rise.jpg",
            likeCount = 98,
            commentCount = 12,
            createdAt = java.time.Instant.now().minusSeconds(3600).toString(),
            liked = false
        ),
        Post(
            id = "mock3",
            externalAuthorId = "user3",
            username = "Sravani",
            profileImageUrl = "https://example.com/profile3.jpg",
            content = "Can't wait for 'Radhe Shyam' to release. Prabhas looks stunning!",
            imageUrl = "https://upload.wikimedia.org/wikipedia/en/9/9c/Radhe_Shyam_poster.jpg",
            likeCount = 110,
            commentCount = 19,
            createdAt = java.time.Instant.now().minusSeconds(1800).toString(),
            liked = false
        ),
        Post(
            id = "mock4",
            externalAuthorId = "user4",
            username = "Priya",
            profileImageUrl = "https://example.com/profile4.jpg",
            content = "'Sye Raa Narasimha Reddy' was a treat for history lovers.",
            imageUrl = "https://upload.wikimedia.org/wikipedia/en/3/3e/Sye_Raa_Narasimha_Reddy_poster.jpg",
            likeCount = 87,
            commentCount = 9,
            createdAt = java.time.Instant.now().minusSeconds(1200).toString(),
            liked = true
        ),
        Post(
            id = "mock5",
            externalAuthorId = "user5",
            username = "Vikram",
            profileImageUrl = "https://example.com/profile5.jpg",
            content = "Looking forward to more content from SS Rajamouli.",
            imageUrl = "https://upload.wikimedia.org/wikipedia/commons/3/3e/S._S._Rajamouli.jpg",
            likeCount = 75,
            commentCount = 8,
            createdAt = java.time.Instant.now().minusSeconds(900).toString(),
            liked = false
        )
    )




    override suspend fun getPosts(startAfter: String?): List<Post> {

        // Return mock data if the flag is set to true
        if (useMockData) {
            return if (startAfter == null) {
                // First page of data
                mockPosts
            } else {
                // For pagination, return empty list to simulate end of data
                emptyList()
            }
        }

        try {
            val response = postApiService.getPosts(
                limit = 20,
                startAfter = startAfter
            )
            
            val json = gson.toJson(response)
            val type = object : TypeToken<Map<String, List<Post>>>() {}.type
            val parsedResponse = gson.fromJson<Map<String, List<Post>>>(json, type)
            
            return parsedResponse["posts"] ?: emptyList()
        } catch (e: Exception) {
            Timber.e(e, "Error fetching posts")
            return emptyList()
        }
    }

    override suspend fun getPostDetail(postId: String): Pair<Post?, List<Comment>> {
        if (useMockData) {
            // Find the post in mock data
            val post = mockPosts.find { it.id == postId }
            // Get comments for this post
            val comments = mockComments[postId] ?: emptyList()
            return Pair(post, comments)
        }

        try {
            val response = postApiService.getPostDetail(postId)
            
            val json = gson.toJson(response)
            val type = object : TypeToken<Map<String, Any>>() {}.type
            val parsedResponse = gson.fromJson<Map<String, Any>>(json, type)
            
            val post = parsedResponse["post"]?.let {
                gson.fromJson(gson.toJson(it), Post::class.java)
            }
            
            val comments = parsedResponse["comments"]?.let {
                gson.fromJson<List<Comment>>(gson.toJson(it), 
                    object : TypeToken<List<Comment>>() {}.type)
            } ?: emptyList()
            
            return Pair(post, comments)
        } catch (e: Exception) {
            Timber.e(e, "Error fetching post detail for $postId")
            return Pair(null, emptyList())
        }
    }

    override suspend fun likePost(postId: String): Boolean {
        if (useMockData) {
            // Simple implementation for mock data
            return true
        }
        try {
            val response = postApiService.likePost(postId)
            
            val json = gson.toJson(response)
            val type = object : TypeToken<Map<String, Boolean>>() {}.type
            val parsedResponse = gson.fromJson<Map<String, Boolean>>(json, type)
            
            return parsedResponse["liked"] ?: false
        } catch (e: Exception) {
            Timber.e(e, "Error liking post $postId")
            return false
        }
    }

    override suspend fun hasUserLiked(postId: String): Boolean {
        try {
            val response = postApiService.hasUserLiked(postId)
            
            val json = gson.toJson(response)
            val type = object : TypeToken<Map<String, Boolean>>() {}.type
            val parsedResponse = gson.fromJson<Map<String, Boolean>>(json, type)
            
            return parsedResponse["liked"] ?: false
        } catch (e: Exception) {
            Timber.e(e, "Error checking like status for post $postId")
            return false
        }
    }

    override suspend fun createPost(text: String?, imageUrl: String?): String? {
        try {
            val data = hashMapOf<String, Any?>().apply {
                put("text", text)
                put("imageUrl", imageUrl)
            }
            
            val response = postApiService.createPost(data)
            
            val json = gson.toJson(response)
            val type = object : TypeToken<Map<String, String>>() {}.type
            val parsedResponse = gson.fromJson<Map<String, String>>(json, type)
            
            return parsedResponse["postId"]
        } catch (e: Exception) {
            Timber.e(e, "Error creating post")
            return null
        }
    }

    override suspend fun editPost(postId: String, text: String?, imageUrl: String?): Boolean {
        try {
            val data = hashMapOf<String, Any?>().apply {
                put("text", text)
                put("imageUrl", imageUrl)
            }
            
            val response = postApiService.editPost(postId, data)
            
            val json = gson.toJson(response)
            val type = object : TypeToken<Map<String, Boolean>>() {}.type
            val parsedResponse = gson.fromJson<Map<String, Boolean>>(json, type)
            
            return parsedResponse["success"] ?: false
        } catch (e: Exception) {
            Timber.e(e, "Error editing post $postId")
            return false
        }
    }

    override suspend fun deletePost(postId: String): Boolean {
        try {
            val response = postApiService.deletePost(postId)
            
            val json = gson.toJson(response)
            val type = object : TypeToken<Map<String, Boolean>>() {}.type
            val parsedResponse = gson.fromJson<Map<String, Boolean>>(json, type)
            
            return parsedResponse["success"] ?: false
        } catch (e: Exception) {
            Timber.e(e, "Error deleting post $postId")
            return false
        }
    }

    override suspend fun reportPost(postId: String, reason: String): Boolean {
        try {
            val data = hashMapOf<String, Any>().apply {
                put("reason", reason)
            }
            
            val response = postApiService.reportPost(postId, data)
            
            val json = gson.toJson(response)
            val type = object : TypeToken<Map<String, Boolean>>() {}.type
            val parsedResponse = gson.fromJson<Map<String, Boolean>>(json, type)
            
            return parsedResponse["success"] ?: false
        } catch (e: Exception) {
            Timber.e(e, "Error reporting post $postId")
            return false
        }
    }

    override suspend fun getComments(postId: String): List<Comment> {
        if (useMockData) {
            return mockComments[postId] ?: emptyList()
        }
        try {
            val response = postApiService.getComments(postId)
            
            val json = gson.toJson(response)
            val type = object : TypeToken<Map<String, List<Comment>>>() {}.type
            val parsedResponse = gson.fromJson<Map<String, List<Comment>>>(json, type)
            
            return parsedResponse["comments"] ?: emptyList()
        } catch (e: Exception) {
            Timber.e(e, "Error fetching comments for post $postId")
            return emptyList()
        }
    }

    override suspend fun submitComment(
        postId: String,
        text: String,
        parentCommentId: String?,
        imageUrl: String?
    ): String? {
        if (useMockData) {
            // Generate a mock comment ID
            return "mock-comment-${System.currentTimeMillis()}"
        }
        try {
            val data = hashMapOf<String, Any?>().apply {
                put("text", text)
                put("parentCommentId", parentCommentId)
                put("imageUrl", imageUrl)
            }
            
            val response = postApiService.submitComment(postId, data)
            
            val json = gson.toJson(response)
            val type = object : TypeToken<Map<String, String>>() {}.type
            val parsedResponse = gson.fromJson<Map<String, String>>(json, type)
            
            return parsedResponse["commentId"]
        } catch (e: Exception) {
            Timber.e(e, "Error submitting comment for post $postId")
            return null
        }
    }

    override suspend fun deleteComment(postId: String, commentId: String): Boolean {
        if (useMockData) {
            // Just return success for mock implementation
            return true
        }
        try {
            val response = postApiService.deleteComment(postId, commentId)
            
            val json = gson.toJson(response)
            val type = object : TypeToken<Map<String, Boolean>>() {}.type
            val parsedResponse = gson.fromJson<Map<String, Boolean>>(json, type)
            
            return parsedResponse["success"] ?: false
        } catch (e: Exception) {
            Timber.e(e, "Error deleting comment $commentId from post $postId")
            return false
        }
    }

    override suspend fun reportComment(postId: String, commentId: String, reason: String): Boolean {
        if (useMockData) {
            // Just return success for mock implementation
            return true
        }
        try {
            val data = hashMapOf<String, Any>().apply {
                put("reason", reason)
            }
            
            val response = postApiService.reportComment(postId, commentId, data)
            
            val json = gson.toJson(response)
            val type = object : TypeToken<Map<String, Boolean>>() {}.type
            val parsedResponse = gson.fromJson<Map<String, Boolean>>(json, type)
            
            return parsedResponse["success"] ?: false
        } catch (e: Exception) {
            Timber.e(e, "Error reporting comment $commentId")
            return false
        }
    }

    override suspend fun getReplies(postId: String, commentId: String): List<Comment> {
        if (useMockData) {
            return mockReplies[commentId] ?: emptyList()
        }
        try {
            val response = postApiService.getReplies(postId, commentId)
            
            val json = gson.toJson(response)
            val type = object : TypeToken<Map<String, List<Comment>>>() {}.type
            val parsedResponse = gson.fromJson<Map<String, List<Comment>>>(json, type)
            
            return parsedResponse["replies"] ?: emptyList()
        } catch (e: Exception) {
            Timber.e(e, "Error fetching replies for comment $commentId")
            return emptyList()
        }
    }

    override suspend fun getImageUploadUrl(fileName: String, contentType: String): Pair<String, String>? {
        if (useMockData) {
            // Return mock URLs for testing
            val mockUploadUrl = "https://mockupload.example.com/${fileName}"
            val mockPublicUrl = "https://mockcdn.example.com/${fileName}"
            return Pair(mockUploadUrl, mockPublicUrl)
        }
        try {
            val data = hashMapOf<String, Any>().apply {
                put("fileName", fileName)
                put("contentType", contentType)
            }
            
            val response = postApiService.getImageUploadUrl(data)
            
            val json = gson.toJson(response)
            val type = object : TypeToken<Map<String, String>>() {}.type
            val parsedResponse = gson.fromJson<Map<String, String>>(json, type)
            
            val uploadUrl = parsedResponse["uploadUrl"]
            val publicUrl = parsedResponse["publicUrl"]
            
            return if (uploadUrl != null && publicUrl != null) {
                Pair(uploadUrl, publicUrl)
            } else {
                null
            }
        } catch (e: Exception) {
            Timber.e(e, "Error getting image upload URL")
            return null
        }
    }
}
